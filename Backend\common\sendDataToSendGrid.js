import axios from "axios";
import { getConfigValue } from "./ssmConfig.js";
/**
 * Send email data to SendGrid
 * @param to - Primary recipient email address
 * @param from - Sender email address
 * @param replyTo - Reply-to email address
 * @param templateId - SendGrid template ID
 * @param dynamicData - Dynamic template data
 * @returns Promise with status and message/error
 */
const sendDataToSendGrid = async (to, from, replyTo, templateId, dynamicData) => {
    try {
        // Get SendGrid API key and optional second recipient from SSM configuration
        const [sendgridApiKey, secondRecipient] = await Promise.all([
            getConfigValue('SENDGRID_API_KEY'),
            getConfigValue('SECOND_RECIPIENT').catch(() => undefined) // Optional parameter
        ]);
        const config = {
            headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + sendgridApiKey,
            },
        };
        const mailTo = secondRecipient
            ? [
                {
                    email: to,
                },
                {
                    email: secondRecipient,
                },
            ]
            : [
                {
                    email: to,
                },
            ];
        const data = {
            from: {
                email: from,
            },
            reply_to: {
                email: replyTo,
            },
            personalizations: [
                {
                    to: mailTo,
                    dynamic_template_data: {
                        lead: dynamicData,
                    },
                },
            ],
            template_id: templateId,
        };
        const result = await axios.post("https://api.sendgrid.com/v3/mail/send", data, config);
        if (result.status === 200 || result.status === 202) {
            return {
                status: true,
                message: "Sendgrid API message: Email sent successfully.",
            };
        }
        else {
            throw new Error(`Unexpected status code: ${result.status}`);
        }
    }
    catch (error) {
        console.error("SendGrid error:", error);
        return {
            status: false,
            error: `Sendgrid API message: Error while sending email.`,
        };
    }
};
export default sendDataToSendGrid;
