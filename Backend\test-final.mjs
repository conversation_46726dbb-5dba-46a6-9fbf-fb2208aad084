/**
 * Final test to verify the Lambda deployment package works correctly
 */

import sendDataToHubspot from "./dist/common/sendDataToHubSpot.js";
import sendDataToSendGrid from "./dist/common/sendDataToSendGrid.js";
import currentTimestamp from "./dist/common/currentTimestamp.js";
import sendToSlack from "./dist/common/sendDataToSlack.js";
import { getConfigValue } from "./dist/common/ssmConfig.js";

console.log("🚀 Testing Lambda deployment package...\n");

// Test that all imports are successful
console.log("✅ All imports successful:");
console.log("  - sendDataToHubspot:", typeof sendDataToHubspot);
console.log("  - sendDataToSendGrid:", typeof sendDataToSendGrid);
console.log("  - currentTimestamp:", typeof currentTimestamp);
console.log("  - sendToSlack:", typeof sendToSlack);
console.log("  - getConfigValue:", typeof getConfigValue);

// Test currentTimestamp function
try {
  const timestamp = currentTimestamp();
  console.log("\n✅ currentTimestamp function works:", timestamp);
} catch (error) {
  console.error("\n❌ currentTimestamp function failed:", error.message);
}

console.log("\n🎉 Lambda deployment package is ready!");
console.log("\nNext steps:");
console.log("1. Create a ZIP file of the 'dist' directory contents");
console.log("2. Upload to AWS Lambda");
console.log("3. Set the handler to 'api/contact-us/index.handler' or 'api/ai-readiness/index.handler'");
console.log("4. Configure environment variables and IAM permissions");
console.log("5. Test with sample events");
