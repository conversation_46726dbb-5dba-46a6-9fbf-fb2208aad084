{"Commands:": "Komennot:", "Options:": "Valinnat:", "Examples:": "Esimerkkejä:", "boolean": "totuusarvo", "count": "lukumäärä", "string": "merk<PERSON>jono", "number": "numero", "array": "taulukko", "required": "<PERSON><PERSON><PERSON><PERSON>", "default": "oletusarvo", "default:": "oletusarvo:", "choices:": "vaihtoehdot:", "aliases:": "aliakset:", "generated-value": "generoitu-arvo", "Not enough non-option arguments: got %s, need at least %s": {"one": "<PERSON><PERSON>, jotka eivät ole valintoja: annettu %s, vaa<PERSON><PERSON> vähintään %s", "other": "<PERSON><PERSON>, jotka eivät ole valintoja: annettu %s, vaa<PERSON><PERSON> vähintään %s"}, "Too many non-option arguments: got %s, maximum of %s": {"one": "<PERSON><PERSON><PERSON>, jotka eivät ole valintoja: annettu %s, salli<PERSON><PERSON> enintään %s", "other": "<PERSON><PERSON><PERSON>, jotka eivät ole valintoja: annettu %s, salli<PERSON><PERSON> enintään %s"}, "Missing argument value: %s": {"one": "Argumentin arvo puuttuu: %s", "other": "Argumentin arvot puuttuvat: %s"}, "Missing required argument: %s": {"one": "Pakollinen argumentti puuttuu: %s", "other": "Pakollisia argumentteja puuttuu: %s"}, "Unknown argument: %s": {"one": "Tuntematon argumentti: %s", "other": "Tuntemattomia argumentteja: %s"}, "Invalid values:": "Virheelliset arvot:", "Argument: %s, Given: %s, Choices: %s": "Argumentti: %s, <PERSON>ttu: %s, <PERSON>aihtoehdot: %s", "Argument check failed: %s": "Arg<PERSON><PERSON> tarkistus epäon<PERSON>ui: %s", "Implications failed:": "Riippuvia argumentteja puuttuu:", "Not enough arguments following: %s": "Argumentin perässä ei ole tarpeeksi argumentteja: %s", "Invalid JSON config file: %s": "Epävalidi JSON-asetustiedosto: %s", "Path to JSON config file": "JSON-as<PERSON><PERSON><PERSON><PERSON> polku", "Show help": "Näytä <PERSON>", "Show version number": "Näytä versionumero", "Did you mean %s?": "Tarkoititko %s?", "Arguments %s and %s are mutually exclusive": "Argumentit %s ja %s eivät ole yhteensopivat", "Positionals:": "Sijaintiparametrit:", "command": "komento"}